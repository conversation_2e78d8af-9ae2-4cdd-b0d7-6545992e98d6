"use client";

import { useState, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, LogOut, Settings, User, Eye, Lock } from "lucide-react";
import { signOut } from "next-auth/react";
import { Session } from "next-auth";

const getPageInfo = (
  pathname: string
): { title: string; status?: "preview" | "tbi" } => {
  switch (pathname) {
    case "/dashboard":
      return { title: "Dashboard", status: "preview" };
    case "/leads":
      return { title: "Leads" };
    case "/lead-generator":
      return { title: "Lead Generator" };
    case "/logs":
      return { title: "Logs" };
    case "/settings":
      return { title: "Settings", status: "tbi" };
    case "/users":
      return { title: "User Management" };
    default:
      return { title: "Bianchi Leads" };
  }
};

interface PageHeaderProps {
  session?: Session | null;
}

export function PageHeader({ session }: PageHeaderProps) {
  const pathname = usePathname();
  const pageInfo = getPageInfo(pathname);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const router = useRouter();

  // Get user from session
  const user = session?.user;

  // For debugging
  useEffect(() => {
    console.log("PageHeader - Session:", session);
    console.log("PageHeader - User:", user);
  }, [session, user]);

  // Determine if user is admin
  const isAdmin = user?.role === "admin";

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      await signOut({ redirect: false });
      router.push("/login");
      router.refresh();
    } catch (error) {
      console.error("Logout failed:", error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <div className="border-b">
      <div className="flex h-16 items-center justify-between px-4">
        <div className="flex items-center gap-4">
          <SidebarTrigger />
          <div className="flex items-center gap-3">
            <h1 className="text-xl font-semibold">{pageInfo.title}</h1>
            {pageInfo.status && (
              <div>
                {pageInfo.status === "preview" && (
                  <Badge
                    variant="outline"
                    className="border-blue-300 text-blue-700 bg-blue-50"
                  >
                    <Eye className="h-3 w-3 mr-1" />
                    TBI - Preview
                  </Badge>
                )}
                {pageInfo.status === "tbi" && (
                  <Badge
                    variant="outline"
                    className="border-amber-300 text-amber-700 bg-amber-50"
                  >
                    <Lock className="h-3 w-3 mr-1" />
                    TBI - Next Sprint
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>

        {user && (
          <div className="flex items-center gap-4">
            <span className="text-gray-700 hidden md:inline-block">
              Welcome, {user.name || "User"}
            </span>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-1">
                  <User className="h-4 w-4" />
                  <span className="hidden md:inline-block">Account</span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>
                  {user.name || "User"} {user.role && `(${user.role})`}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                {isAdmin && (
                  <>
                    <DropdownMenuItem asChild>
                      <Link href="/users" className="w-full">
                        <Settings className="h-4 w-4 mr-2" />
                        User Management
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                  </>
                )}
                <DropdownMenuItem
                  onClick={handleLogout}
                  disabled={isLoggingOut}
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  {isLoggingOut ? "Logging out..." : "Logout"}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>
    </div>
  );
}
