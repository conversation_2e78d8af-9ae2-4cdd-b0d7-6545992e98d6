import { NextResponse } from "next/server";
import { query } from "@/lib/db";

/**
 * Extracts street number from formatted address for PlacesAPI leads
 * @param formattedAddress - The formatted address string
 * @param source - The source of the lead
 * @param existingStreetNumber - The existing street number (if any)
 * @returns The extracted street number or the existing one
 */
function extractStreetNumber(
  formattedAddress: string | null | undefined,
  source: string,
  existingStreetNumber: string | null | undefined
): string {
  // Only apply extraction for PlacesAPI leads
  if (source !== "PlacesAPI") {
    return existingStreetNumber || "";
  }

  // If we already have a street number, use it
  if (existingStreetNumber && existingStreetNumber.trim() !== "") {
    return existingStreetNumber;
  }

  // If no formatted address, return empty
  if (!formattedAddress || formattedAddress.trim() === "") {
    return "";
  }

  // Extract street number using improved regex pattern
  // This pattern looks for numbers (with optional letters) that come after a street name but before a comma
  // It specifically targets the pattern: "StreetName Number, PostalCode City"
  // Pattern: \s([0-9]+[A-Za-z]?)\s*,\s*[0-9]{4}
  let streetNumberMatch = formattedAddress.match(
    /\s([0-9]+[A-Za-z]?)\s*,\s*[0-9]{4}/
  );

  // If that doesn't work, try a simpler pattern that looks for numbers after a space but not at the start
  // This handles cases where there might not be a comma after the street number
  if (!streetNumberMatch) {
    // Look for pattern: "word space number" but not "number space number" (to avoid postal codes)
    streetNumberMatch = formattedAddress.match(
      /[A-Za-z]\s+([0-9]+[A-Za-z]?)(?:\s|,|$)/
    );
  }

  if (streetNumberMatch && streetNumberMatch[1]) {
    console.log(
      `Extracted street number "${streetNumberMatch[1]}" from address: ${formattedAddress}`
    );
    return streetNumberMatch[1];
  }

  // No street number found
  return "";
}

interface LeadsBatchInfo {
  batchIndex: number;
  totalBatches: number;
  batchSize: number;
  totalLeads: number;
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { leads, batchInfo } = body as {
      leads: any[];
      batchInfo?: LeadsBatchInfo;
    };

    console.log("=== ADDING LEADS ===");
    if (batchInfo) {
      console.log(
        `Processing leads batch ${batchInfo.batchIndex}/${batchInfo.totalBatches} with ${batchInfo.batchSize} leads (total: ${batchInfo.totalLeads})`
      );
    } else {
      console.log(`Received ${leads?.length || 0} leads to add`);
    }

    if (!leads || !Array.isArray(leads) || leads.length === 0) {
      console.error("No valid leads provided");
      return NextResponse.json(
        { error: "No valid leads provided" },
        { status: 400 }
      );
    }

    // Log sample lead (just one for debugging)
    console.log("Sample lead:", leads[0]);

    const addedLeads = [];

    // Filter out leads with missing required fields
    const validLeads = leads.filter((lead) => lead.place_id && lead.name);

    if (validLeads.length < leads.length) {
      console.error(
        `Skipped ${
          leads.length - validLeads.length
        } leads with missing required fields`
      );
    }

    // Get all place IDs to check in a single query
    const placeIds = validLeads.map((lead) => lead.place_id);

    // Check which leads already exist in a single query
    const existingResults = await query(
      'SELECT google_place_id FROM "bianchi_leads" WHERE google_place_id = ANY($1::text[])',
      [placeIds]
    );

    // Create a Set of existing place IDs for faster lookups
    const existingPlaceIds = new Set(
      existingResults.map((row) => row.google_place_id)
    );

    // Process only new leads
    for (const lead of validLeads) {
      // Skip if lead already exists
      if (existingPlaceIds.has(lead.place_id)) {
        continue;
      }

      // Minimal logging for new leads
      console.log(`Adding new lead: ${lead.name}`);

      // Extract street number from formatted address for PlacesAPI leads
      const extractedStreetNumber = extractStreetNumber(
        lead.formatted_address || lead.vicinity,
        "PlacesAPI",
        lead.street_number
      );

      // Get longitude and latitude as numbers or null
      const longitude =
        lead.location?.longitude !== undefined &&
        lead.location?.longitude !== null
          ? parseFloat(String(lead.location.longitude))
          : null;

      const latitude =
        lead.location?.latitude !== undefined &&
        lead.location?.latitude !== null
          ? parseFloat(String(lead.location.latitude))
          : null;

      // Determine if we should set the location point
      const hasValidCoordinates =
        longitude !== null &&
        latitude !== null &&
        !isNaN(longitude) &&
        !isNaN(latitude) &&
        longitude !== 0 &&
        latitude !== 0; // Ensure coordinates are not 0,0

      // Reduced coordinate logging
      if (hasValidCoordinates) {
        console.log(`Valid coordinates: ${longitude}, ${latitude}`);
      }

      // Add new lead with all available data
      // Note: ST_MakePoint takes (longitude, latitude) in that order
      const result = await query(
        `INSERT INTO "bianchi_leads" (
          source,
          google_place_id,
          erp_id,
          name,
          formatted_address,
          street_name,
          street_number,
          city,
          postal_code,
          canton,
          country,
          business_status,
          location,
          types,
          created_at,
          updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12,
          ${
            hasValidCoordinates
              ? "ST_SetSRID(ST_MakePoint($13, $14), 4326)"
              : "NULL"
          },
          $15::text[], NOW(), NOW()) RETURNING id`,
        [
          "PlacesAPI", // source
          lead.place_id,
          lead.erp_id || null,
          lead.name,
          lead.formatted_address || lead.vicinity || "",
          lead.street_name || "",
          extractedStreetNumber, // Use extracted street number
          lead.city || "",
          lead.postal_code || "",
          lead.canton || "",
          lead.country || "Switzerland",
          lead.business_status || "UNKNOWN",
          ...(hasValidCoordinates ? [longitude, latitude] : []),
          lead.types || [],
        ]
      );

      // Minimal logging for successful inserts
      console.log(`Lead inserted with ID: ${result[0].id}`);

      // Skip verification query to improve performance
      addedLeads.push(result[0].id);
    }

    console.log(`Successfully added ${addedLeads.length} new leads`);

    return NextResponse.json({
      success: true,
      added: addedLeads.length,
      message: `Added ${addedLeads.length} new leads`,
    });
  } catch (error) {
    console.error("Failed to add leads:", error);
    return NextResponse.json(
      {
        error: "Failed to add leads",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
