import { NextResponse } from "next/server";

const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY;
const PLACES_API_URL = "https://places.googleapis.com/v1/places:searchText";

// Delay function to avoid hitting rate limits
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

interface LeadWithoutPlaceId {
  id: number;
  name: string;
  formatted_address: string;
  street_name: string;
  street_number: string;
  city: string;
  postal_code: string;
  canton: string;
  country: string;
  international_phone: string | null;
  national_phone: string | null;
  location: {
    latitude: number;
    longitude: number;
  } | null;
}

interface PlaceIdSearchResult {
  leadId: number;
  leadName: string;
  placeId: string | null;
  googleName: string | null;
  confidence: "high" | "medium" | "failed";
  searchQuery: string;
  reason?: string;
}

function constructSearchQuery(lead: LeadWithoutPlaceId): string {
  // Build comprehensive search query using available data
  const parts = [];

  if (lead.name) parts.push(lead.name);

  // Add address components
  if (lead.street_name && lead.street_number) {
    parts.push(`${lead.street_number} ${lead.street_name}`);
  } else if (lead.street_name) {
    parts.push(lead.street_name);
  }

  if (lead.city) parts.push(lead.city);
  if (lead.postal_code) parts.push(lead.postal_code);
  if (lead.canton) parts.push(lead.canton);
  if (lead.country) parts.push(lead.country);

  return parts.filter(Boolean).join(", ");
}

function determineConfidence(hasLocationBias: boolean): "high" | "medium" {
  // Simplified confidence based only on location bias
  // Since Google's algorithm selected this as the best match, we trust it
  if (hasLocationBias) {
    return "high"; // Single result with location bias = high confidence
  } else {
    return "medium"; // Single result without location bias = medium confidence
  }
}

async function searchPlaceId(
  lead: LeadWithoutPlaceId
): Promise<PlaceIdSearchResult> {
  const searchQuery = constructSearchQuery(lead);

  if (!searchQuery) {
    return {
      leadId: lead.id,
      leadName: lead.name,
      placeId: "None", // Set to "None" instead of null for insufficient data
      googleName: null,
      confidence: "failed",
      searchQuery: "",
      reason: "Insufficient data for search query",
    };
  }

  try {
    // Prepare request body with location bias if available
    const requestBody: any = {
      textQuery: searchQuery,
      maxResultCount: 1, // Get only the most relevant result
    };

    const hasLocationBias = !!lead.location;

    // Add location bias if coordinates are available
    if (hasLocationBias && lead.location) {
      requestBody.locationBias = {
        circle: {
          center: {
            latitude: lead.location.latitude,
            longitude: lead.location.longitude,
          },
          radius: 1000, // 1km radius
        },
      };
    }

    const response = await fetch(PLACES_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Goog-Api-Key": GOOGLE_MAPS_API_KEY!,
        // Use only ID Only SKU fields to minimize costs
        "X-Goog-FieldMask": "places.id,nextPageToken",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Places API error for lead ${lead.id}:`, errorText);
      return {
        leadId: lead.id,
        leadName: lead.name,
        placeId: "None", // Set to "None" instead of null for API errors
        googleName: null,
        confidence: "failed",
        searchQuery,
        reason: `API error: ${response.status}`,
      };
    }

    const data = await response.json();
    const places = data.places || [];

    console.log(`\n=== OPTIMIZED SEARCH for "${lead.name}" ===`);
    console.log(`Search Query: "${searchQuery}"`);
    console.log(`Location Bias: ${hasLocationBias}`);
    console.log(`Found ${places.length} places from API`);

    if (places.length === 0) {
      console.log(`No places found for "${lead.name}"`);
      return {
        leadId: lead.id,
        leadName: lead.name,
        placeId: "None", // Set to "None" instead of null for no results
        googleName: null,
        confidence: "failed",
        searchQuery,
        reason: "No places found",
      };
    }

    // Since we requested maxResultCount: 1, we should have exactly 1 result
    // Trust Google's algorithm - if it returned this result, it's the best match
    const place = places[0];
    const confidence = determineConfidence(hasLocationBias);

    console.log(`Place ID: ${place.id}`);
    console.log(
      `Confidence: ${confidence} (based on location bias: ${hasLocationBias})`
    );

    return {
      leadId: lead.id,
      leadName: lead.name,
      placeId: place.id,
      googleName: null, // Not available in ID Only SKU
      confidence,
      searchQuery,
    };
  } catch (error) {
    console.error(`Error searching for lead ${lead.id}:`, error);
    return {
      leadId: lead.id,
      leadName: lead.name,
      placeId: "None", // Set to "None" instead of null for search errors
      googleName: null,
      confidence: "failed",
      searchQuery,
      reason: `Search error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

interface BatchInfo {
  batchIndex: number;
  totalBatches: number;
  batchSize: number;
  totalLeads: number;
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { leads, batchInfo } = body as {
      leads: LeadWithoutPlaceId[];
      batchInfo?: BatchInfo;
    };

    if (!leads || !Array.isArray(leads)) {
      return NextResponse.json(
        { error: "Invalid leads data" },
        { status: 400 }
      );
    }

    if (!GOOGLE_MAPS_API_KEY) {
      return NextResponse.json(
        { error: "Google Maps API key not configured" },
        { status: 500 }
      );
    }

    // Log batch information if provided
    if (batchInfo) {
      console.log(
        `Processing batch ${batchInfo.batchIndex}/${batchInfo.totalBatches} with ${batchInfo.batchSize} leads (${leads.length} total leads: ${batchInfo.totalLeads})`
      );
    }

    // Create a streaming response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      async start(controller) {
        let completed = 0;
        let successful = 0;
        let failed = 0;
        let isStreamClosed = false;

        // Helper function to safely enqueue data
        const safeEnqueue = (data: any): boolean => {
          if (isStreamClosed) {
            console.log("Stream already closed, skipping enqueue");
            return false;
          }

          try {
            controller.enqueue(encoder.encode(JSON.stringify(data) + "\n"));
            return true;
          } catch (error) {
            console.error("Error enqueueing data:", error);
            isStreamClosed = true;
            return false;
          }
        };

        // Helper function to safely close the controller
        const safeClose = () => {
          if (!isStreamClosed) {
            try {
              controller.close();
              isStreamClosed = true;
            } catch (error) {
              console.error("Error closing controller:", error);
              isStreamClosed = true;
            }
          }
        };

        try {
          for (const lead of leads) {
            // Check if stream is still open before processing
            if (isStreamClosed) {
              console.log("Stream closed, stopping search early");
              break;
            }

            // Search for Place ID
            const result = await searchPlaceId(lead);

            // Update counters
            completed++;
            if (result.placeId && result.placeId !== "None") {
              successful++;
            } else {
              failed++;
            }

            // Send progress update
            const progressData = {
              type: "progress",
              completed,
              successful,
              failed,
              total: leads.length,
            };

            if (!safeEnqueue(progressData)) {
              console.log("Failed to send progress, stream likely closed");
              break;
            }

            // Send result
            const resultData = {
              type: "result",
              result,
            };

            if (!safeEnqueue(resultData)) {
              console.log("Failed to send result, stream likely closed");
              break;
            }

            // Add delay to avoid rate limiting (shorter delay as requested)
            await delay(100); // 100ms delay instead of 1000ms
          }

          // Send completion signal only if stream is still open
          if (!isStreamClosed) {
            const completeData = {
              type: "complete",
              totalProcessed: completed,
              totalFound: successful,
              totalFailed: failed,
            };
            safeEnqueue(completeData);
          }
        } catch (error) {
          console.error("Error in bulk search stream:", error);

          // Only send error if stream is still open
          if (!isStreamClosed) {
            const errorData = {
              type: "error",
              message: error instanceof Error ? error.message : "Unknown error",
            };
            safeEnqueue(errorData);
          }
        } finally {
          safeClose();
        }
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      },
    });
  } catch (error) {
    console.error("Error in bulk Place ID search:", error);
    return NextResponse.json(
      {
        error: "Failed to process bulk search",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
