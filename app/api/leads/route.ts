import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/db";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import {
  cacheManager,
  generateLeadsCacheKey,
  generateLeadsCountCacheKey,
} from "@/lib/cache";

// Define the base fields that are always selected for performance
const BASE_FIELDS = `
  id,
  source,
  google_place_id,
  erp_id,
  name,
  formatted_address,
  street_name,
  street_number,
  city,
  postal_code,
  canton,
  country,
  international_phone,
  national_phone,
  website_uri,
  business_status,
  created_at,
  updated_at,
  address_group_description
`;

// Extended fields for detailed view
const EXTENDED_FIELDS = `
  ${BASE_FIELDS},
  price_level,
  rating,
  user_rating_count,
  types,
  ST_X(location::geometry) as longitude,
  ST_Y(location::geometry) as latitude,
  plus_code,
  approved,
  language_code,
  language_description,
  name1,
  name2,
  name3,
  email,
  contact_person_email,
  address_group,
  representative1,
  representative2,
  parent_group,
  parent_group_description,
  "group",
  group_description,
  business_type,
  business_type_description,
  salutation_number,
  salutation_description,
  contact_person_first_name,
  contact_person_last_name
`;

interface LeadsQueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  includeExtended?: boolean;
  filters?: {
    canton?: string;
    businessStatus?: string;
    addressGroupDescription?: string;
    source?: string;
    hasPlaceId?: "true" | "false" | "none";
    hasLocation?: "true" | "false";
    approved?: "true" | "false";
    timePeriod?: string;
    showDuplicatesOnly?: "true" | "false";
    postalCode?: string;
    newLeads?: "true" | "false";
  };
}

function buildWhereClause(
  search?: string,
  filters?: LeadsQueryParams["filters"]
) {
  let whereClause = "WHERE country = 'Switzerland'";
  const queryParams: any[] = [];
  let paramIndex = 1;

  // Search functionality - search in name and formatted_address
  if (search) {
    whereClause += ` AND (name ILIKE $${paramIndex} OR formatted_address ILIKE $${
      paramIndex + 1
    })`;
    queryParams.push(`%${search}%`, `%${search}%`);
    paramIndex += 2;
  }

  // Filter by canton
  if (filters?.canton) {
    whereClause += ` AND canton = $${paramIndex}`;
    queryParams.push(filters.canton);
    paramIndex++;
  }

  // Filter by business status
  if (filters?.businessStatus) {
    whereClause += ` AND business_status = $${paramIndex}`;
    queryParams.push(filters.businessStatus);
    paramIndex++;
  }

  // Filter by address group description
  if (filters?.addressGroupDescription) {
    if (filters.addressGroupDescription === "__empty__") {
      whereClause += ` AND (address_group_description IS NULL OR address_group_description = '')`;
    } else {
      whereClause += ` AND address_group_description = $${paramIndex}`;
      queryParams.push(filters.addressGroupDescription);
      paramIndex++;
    }
  }

  // Filter by source
  if (filters?.source) {
    whereClause += ` AND source = $${paramIndex}`;
    queryParams.push(filters.source);
    paramIndex++;
  }

  // Filter by Google Place ID status
  if (filters?.hasPlaceId) {
    if (filters.hasPlaceId === "true") {
      whereClause += ` AND google_place_id IS NOT NULL AND google_place_id != '' AND google_place_id != 'None'`;
    } else if (filters.hasPlaceId === "false") {
      whereClause += ` AND (google_place_id IS NULL OR google_place_id = '')`;
    } else if (filters.hasPlaceId === "none") {
      whereClause += ` AND google_place_id = 'None'`;
    }
  }

  // Filter by location data
  if (filters?.hasLocation) {
    if (filters.hasLocation === "true") {
      whereClause += ` AND location IS NOT NULL`;
    } else {
      whereClause += ` AND location IS NULL`;
    }
  }

  // Filter by approval status
  if (filters?.approved) {
    whereClause += ` AND approved = $${paramIndex}`;
    queryParams.push(filters.approved === "true");
    paramIndex++;
  }

  // Filter by time period
  if (filters?.timePeriod && filters.timePeriod !== "all") {
    const days = parseInt(filters.timePeriod);
    if (!isNaN(days)) {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      whereClause += ` AND created_at >= $${paramIndex}`;
      queryParams.push(cutoffDate.toISOString());
      paramIndex++;
    }
  }

  // Filter to show only duplicates - prioritize ERP leads
  if (filters?.showDuplicatesOnly === "true") {
    whereClause += ` AND (
      (source = 'ERP' AND google_place_id IN (
        SELECT google_place_id
        FROM "bianchi_leads"
        WHERE google_place_id IS NOT NULL
          AND google_place_id != ''
          AND google_place_id != 'None'
          AND country = 'Switzerland'
        GROUP BY google_place_id
        HAVING COUNT(*) > 1
      ))
      OR
      (source != 'ERP' AND google_place_id IN (
        SELECT google_place_id
        FROM "bianchi_leads"
        WHERE google_place_id IS NOT NULL
          AND google_place_id != ''
          AND google_place_id != 'None'
          AND country = 'Switzerland'
        GROUP BY google_place_id
        HAVING COUNT(*) > 1
          AND COUNT(CASE WHEN source = 'ERP' THEN 1 END) = 0
      ))
    )`;
  }

  // Filter by postal code
  if (filters?.postalCode) {
    whereClause += ` AND postal_code ILIKE $${paramIndex}`;
    queryParams.push(`%${filters.postalCode}%`);
    paramIndex++;
  }

  // Filter for new leads (PlacesAPI leads without matching ERP leads)
  if (filters?.newLeads === "true") {
    whereClause += ` AND source = 'PlacesAPI' AND (
      google_place_id IS NULL
      OR google_place_id = ''
      OR google_place_id = 'None'
      OR google_place_id NOT IN (
        SELECT DISTINCT google_place_id
        FROM "bianchi_leads"
        WHERE source = 'ERP'
          AND google_place_id IS NOT NULL
          AND google_place_id != ''
          AND google_place_id != 'None'
          AND country = 'Switzerland'
      )
    )`;
  }

  return { whereClause, queryParams, paramIndex };
}

async function getLeadsCount(
  search?: string,
  filters?: LeadsQueryParams["filters"]
): Promise<number> {
  const cacheKey = generateLeadsCountCacheKey({ search, filters });

  // Check cache first
  const cachedCount = cacheManager.get<number>(cacheKey);
  if (cachedCount !== null && !cacheManager.isStale(cacheKey)) {
    return cachedCount;
  }

  const { whereClause, queryParams } = buildWhereClause(search, filters);

  const countQuery = `
    SELECT COUNT(*) as total
    FROM "bianchi_leads"
    ${whereClause}
  `;

  const result = await query(countQuery, queryParams);
  const count = parseInt(result[0]?.total || "0");

  // Cache the result
  cacheManager.set(cacheKey, count);

  return count;
}

async function getLeads(params: LeadsQueryParams) {
  const {
    page = 1,
    pageSize = 25,
    search = "",
    sortBy = "created_at",
    sortOrder = "desc",
    includeExtended = false,
    filters = {},
  } = params;

  const cacheKey = generateLeadsCacheKey({
    page,
    pageSize,
    search,
    sortBy,
    sortOrder,
    filters,
  });

  // Check cache first (stale-while-revalidate)
  const cachedData = cacheManager.get<any>(cacheKey);
  const isStale = cacheManager.isStale(cacheKey);

  // If we have fresh data, return it immediately
  if (cachedData && !isStale) {
    return cachedData;
  }

  // Build the query
  const fields = includeExtended ? EXTENDED_FIELDS : BASE_FIELDS;
  const offset = (page - 1) * pageSize;

  // Build WHERE clause using shared function
  const { whereClause, queryParams, paramIndex } = buildWhereClause(
    search,
    filters
  );

  // Build ORDER BY clause
  const validSortFields = [
    "created_at",
    "updated_at",
    "name",
    "city",
    "canton",
  ];
  const safeSortBy = validSortFields.includes(sortBy) ? sortBy : "created_at";
  const safeSortOrder = sortOrder === "asc" ? "ASC" : "DESC";

  const leadsQuery = `
    SELECT ${fields}
    FROM "bianchi_leads"
    ${whereClause}
    ORDER BY
      CASE WHEN source = 'ERP' THEN 0 ELSE 1 END,
      ${safeSortBy} ${safeSortOrder}
    LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
  `;

  queryParams.push(pageSize, offset);

  // Execute the query
  const leads = await query(leadsQuery, queryParams);

  // Transform the data to include location as an object
  const transformedLeads = leads.map((lead) => ({
    ...lead,
    location:
      lead.longitude && lead.latitude
        ? {
            latitude: parseFloat(lead.latitude),
            longitude: parseFloat(lead.longitude),
          }
        : null,
    // Remove the separate longitude/latitude fields
    longitude: undefined,
    latitude: undefined,
    created_at: new Date(lead.created_at).toISOString(),
    updated_at: lead.updated_at
      ? new Date(lead.updated_at).toISOString()
      : null,
  }));

  const result = {
    leads: transformedLeads,
    pagination: {
      page,
      pageSize,
      total: await getLeadsCount(search, filters),
      totalPages: Math.ceil((await getLeadsCount(search, filters)) / pageSize),
    },
  };

  // Cache the result
  cacheManager.set(cacheKey, result);

  return result;
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "25");
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "created_at";
    const sortOrder = (searchParams.get("sortOrder") || "desc") as
      | "asc"
      | "desc";
    const includeExtended = searchParams.get("includeExtended") === "true";

    // Parse individual filter parameters
    const filters: LeadsQueryParams["filters"] = {};

    if (searchParams.get("canton")) {
      filters.canton = searchParams.get("canton")!;
    }
    if (searchParams.get("businessStatus")) {
      filters.businessStatus = searchParams.get("businessStatus")!;
    }
    if (searchParams.get("addressGroupDescription")) {
      filters.addressGroupDescription = searchParams.get(
        "addressGroupDescription"
      )!;
    }
    if (searchParams.get("source")) {
      filters.source = searchParams.get("source")!;
    }
    if (searchParams.get("hasPlaceId")) {
      filters.hasPlaceId = searchParams.get("hasPlaceId") as
        | "true"
        | "false"
        | "none";
    }
    if (searchParams.get("hasLocation")) {
      filters.hasLocation = searchParams.get("hasLocation") as "true" | "false";
    }
    if (searchParams.get("approved")) {
      filters.approved = searchParams.get("approved") as "true" | "false";
    }
    if (searchParams.get("timePeriod")) {
      filters.timePeriod = searchParams.get("timePeriod")!;
    }
    if (searchParams.get("showDuplicatesOnly")) {
      filters.showDuplicatesOnly = searchParams.get("showDuplicatesOnly") as
        | "true"
        | "false";
    }
    if (searchParams.get("postalCode")) {
      filters.postalCode = searchParams.get("postalCode")!;
    }
    if (searchParams.get("newLeads")) {
      filters.newLeads = searchParams.get("newLeads") as "true" | "false";
    }

    // Also support legacy filters parameter for backward compatibility
    const filtersParam = searchParams.get("filters");
    if (filtersParam) {
      try {
        const legacyFilters = JSON.parse(filtersParam);
        Object.assign(filters, legacyFilters);
      } catch (e) {
        // Invalid JSON, ignore legacy filters
      }
    }

    const params: LeadsQueryParams = {
      page,
      pageSize,
      search,
      sortBy,
      sortOrder,
      includeExtended,
      filters,
    };

    const result = await getLeads(params);

    // For backward compatibility, if no pagination params are provided,
    // return the old format
    if (!searchParams.has("page") && !searchParams.has("pageSize")) {
      return NextResponse.json({ leads: result.leads });
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error fetching leads:", error);
    return NextResponse.json(
      { error: "Failed to fetch leads" },
      { status: 500 }
    );
  }
}
