import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { query } from "@/lib/db";
import { cacheManager } from "@/lib/cache";

// Cache duplicate information for 10 minutes
const DUPLICATES_CACHE_KEY = "leads:duplicates";
const DUPLICATES_TTL = 10 * 60 * 1000; // 10 minutes

interface DuplicateGroup {
  google_place_id: string;
  count: number;
  lead_ids: string[];
  erp_count: number;
  places_api_count: number;
}

interface DuplicatesInfo {
  duplicateGroups: DuplicateGroup[];
  totalDuplicateLeads: number;
  duplicateGroupsCount: number;
}

async function getDuplicatesInfo(): Promise<DuplicatesInfo> {
  // Check cache first
  const cached = cacheManager.get<DuplicatesInfo>(DUPLICATES_CACHE_KEY);
  if (cached && !cacheManager.isStale(DUPLICATES_CACHE_KEY)) {
    return cached;
  }

  // Find all leads with duplicate Google Place IDs, prioritizing ERP leads
  const duplicatesQuery = `
    SELECT
      google_place_id,
      COUNT(*) as count,
      ARRAY_AGG(id ORDER BY CASE WHEN source = 'ERP' THEN 0 ELSE 1 END, created_at) as lead_ids,
      COUNT(CASE WHEN source = 'ERP' THEN 1 END) as erp_count,
      COUNT(CASE WHEN source != 'ERP' THEN 1 END) as places_api_count
    FROM "bianchi_leads"
    WHERE
      country = 'Switzerland'
      AND google_place_id IS NOT NULL
      AND google_place_id != ''
      AND google_place_id != 'None'
    GROUP BY google_place_id
    HAVING COUNT(*) > 1
    ORDER BY COUNT(*) DESC, google_place_id
  `;

  const duplicateResults = await query(duplicatesQuery);

  const duplicateGroups: DuplicateGroup[] = duplicateResults.map((row) => ({
    google_place_id: row.google_place_id,
    count: parseInt(row.count),
    lead_ids: row.lead_ids,
    erp_count: parseInt(row.erp_count),
    places_api_count: parseInt(row.places_api_count),
  }));

  const totalDuplicateLeads = duplicateGroups.reduce(
    (sum, group) => sum + group.count,
    0
  );
  const duplicateGroupsCount = duplicateGroups.length;

  const duplicatesInfo: DuplicatesInfo = {
    duplicateGroups,
    totalDuplicateLeads,
    duplicateGroupsCount,
  };

  // Cache the result
  cacheManager.set(DUPLICATES_CACHE_KEY, duplicatesInfo, DUPLICATES_TTL);

  return duplicatesInfo;
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const placeId = searchParams.get("placeId");

    // If placeId is provided, return all leads for that specific place ID
    if (placeId) {
      const leadsQuery = `
        SELECT
          id,
          source,
          google_place_id,
          erp_id,
          name,
          formatted_address,
          street_name,
          street_number,
          city,
          postal_code,
          canton,
          country,
          international_phone,
          national_phone,
          website_uri,
          business_status,
          created_at,
          updated_at,
          address_group_description,
          ST_X(location::geometry) as longitude,
          ST_Y(location::geometry) as latitude,
          approved
        FROM "bianchi_leads"
        WHERE google_place_id = $1 AND country = 'Switzerland'
        ORDER BY
          CASE WHEN source = 'ERP' THEN 0 ELSE 1 END,
          created_at DESC
      `;

      const leads = await query(leadsQuery, [placeId]);

      // Transform the data to include location as an object
      const transformedLeads = leads.map((lead) => ({
        ...lead,
        location:
          lead.longitude && lead.latitude
            ? {
                latitude: parseFloat(lead.latitude),
                longitude: parseFloat(lead.longitude),
              }
            : null,
        // Remove the separate longitude/latitude fields
        longitude: undefined,
        latitude: undefined,
        created_at: new Date(lead.created_at).toISOString(),
        updated_at: lead.updated_at
          ? new Date(lead.updated_at).toISOString()
          : null,
      }));

      return NextResponse.json({ leads: transformedLeads });
    }

    // Otherwise, return duplicate groups info
    const duplicatesInfo = await getDuplicatesInfo();
    return NextResponse.json(duplicatesInfo);
  } catch (error) {
    console.error("Error fetching duplicates info:", error);
    return NextResponse.json(
      { error: "Failed to fetch duplicates info" },
      { status: 500 }
    );
  }
}
