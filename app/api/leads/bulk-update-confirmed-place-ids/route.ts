import { NextResponse } from "next/server";
import { query } from "@/lib/db";

interface PlaceIdUpdate {
  leadId: number;
  placeId: string;
  googleName: string | null;
}

interface UpdateBatchInfo {
  batchIndex: number;
  totalBatches: number;
  batchSize: number;
  totalUpdates: number;
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { updates, batchInfo } = body as {
      updates: PlaceIdUpdate[];
      batchInfo?: UpdateBatchInfo;
    };

    // Log batch information if provided
    if (batchInfo) {
      console.log(
        `Processing update batch ${batchInfo.batchIndex}/${batchInfo.totalBatches} with ${batchInfo.batchSize} updates (total: ${batchInfo.totalUpdates})`
      );
    }

    if (!updates || !Array.isArray(updates)) {
      return NextResponse.json(
        { error: "Invalid updates data" },
        { status: 400 }
      );
    }

    if (updates.length === 0) {
      return NextResponse.json(
        { error: "No updates provided" },
        { status: 400 }
      );
    }

    let updatedCount = 0;
    const results = [];

    // Process each update
    for (const update of updates) {
      try {
        const { leadId, placeId, googleName } = update;

        if (!leadId || !placeId) {
          results.push({
            leadId,
            success: false,
            error: "Missing leadId or placeId",
          });
          continue;
        }

        // Update the lead with the Google Place ID (including "None" for no results)
        const result = await query(
          `UPDATE "bianchi_leads"
           SET google_place_id = $1, updated_at = CURRENT_TIMESTAMP
           WHERE id = $2 AND (google_place_id IS NULL OR google_place_id = '')
           RETURNING id, name, google_place_id`,
          [placeId, leadId]
        );

        if (result.length === 0) {
          results.push({
            leadId,
            success: false,
            error: "Lead not found or already has Place ID",
          });
        } else {
          updatedCount++;
          results.push({
            leadId,
            success: true,
            lead: result[0],
            googleName,
          });
        }
      } catch (error) {
        console.error(`Error updating lead ${update.leadId}:`, error);
        results.push({
          leadId: update.leadId,
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return NextResponse.json({
      success: true,
      updated: updatedCount,
      total: updates.length,
      results,
      message: `Successfully updated ${updatedCount} of ${updates.length} leads`,
    });
  } catch (error) {
    console.error("Error in bulk Place ID update:", error);
    return NextResponse.json(
      {
        error: "Failed to update Place IDs",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
