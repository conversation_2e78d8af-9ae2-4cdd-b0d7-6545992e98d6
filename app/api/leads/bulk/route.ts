import { NextResponse } from "next/server";
import { query } from "@/lib/db";

/**
 * Extracts street number from formatted address for PlacesAPI leads
 * @param formattedAddress - The formatted address string
 * @param source - The source of the lead
 * @param existingStreetNumber - The existing street number (if any)
 * @returns The extracted street number or the existing one
 */
function extractStreetNumber(
  formattedAddress: string | null | undefined,
  source: string,
  existingStreetNumber: string | null | undefined
): string {
  // Only apply extraction for PlacesAPI leads
  if (source !== "PlacesAPI") {
    return existingStreetNumber || "";
  }

  // If we already have a street number, use it
  if (existingStreetNumber && existingStreetNumber.trim() !== "") {
    return existingStreetNumber;
  }

  // If no formatted address, return empty
  if (!formattedAddress || formattedAddress.trim() === "") {
    return "";
  }

  // Extract street number using improved regex pattern
  // This pattern looks for numbers (with optional letters) that come after a street name but before a comma
  // It specifically targets the pattern: "StreetName Number, PostalCode City"
  // Pattern: \s([0-9]+[A-Za-z]?)\s*,\s*[0-9]{4}
  let streetNumberMatch = formattedAddress.match(
    /\s([0-9]+[A-Za-z]?)\s*,\s*[0-9]{4}/
  );

  // If that doesn't work, try a simpler pattern that looks for numbers after a space but not at the start
  // This handles cases where there might not be a comma after the street number
  if (!streetNumberMatch) {
    // Look for pattern: "word space number" but not "number space number" (to avoid postal codes)
    streetNumberMatch = formattedAddress.match(
      /[A-Za-z]\s+([0-9]+[A-Za-z]?)(?:\s|,|$)/
    );
  }

  if (streetNumberMatch && streetNumberMatch[1]) {
    console.log(
      `Extracted street number "${streetNumberMatch[1]}" from address: ${formattedAddress}`
    );
    return streetNumberMatch[1];
  }

  // No street number found
  return "";
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { places, source } = body;

    const addedLeads = [];

    for (const place of places) {
      // Check if lead already exists by Google Place ID or ERP ID
      const existing = await query(
        'SELECT id FROM "bianchi_leads" WHERE google_place_id = $1 OR erp_id = $2',
        [place.place_id, place.erp_id]
      );

      if (existing.length === 0) {
        // Extract street number from formatted address for PlacesAPI leads
        const extractedStreetNumber = extractStreetNumber(
          place.formatted_address,
          source,
          place.street_number
        );

        // Add new lead with all available data
        const result = await query(
          `INSERT INTO "bianchi_leads" (
            source,
            google_place_id,
            erp_id,
            name,
            formatted_address,
            street_name,
            street_number,
            city,
            postal_code,
            canton,
            country,
            business_status,
            location,
            types,
            address_components,
            created_at,
            updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, ST_SetSRID(ST_MakePoint($13, $14), 4326), $15::text[], $16::jsonb, NOW(), NOW()) RETURNING id`,
          [
            source,
            place.place_id,
            place.erp_id,
            place.name,
            place.formatted_address,
            place.street_name,
            extractedStreetNumber, // Use extracted street number
            place.city,
            place.postal_code,
            place.canton,
            place.country,
            place.business_status,
            place.location?.longitude,
            place.location?.latitude,
            place.types || [],
            JSON.stringify(place.address_components || {}),
          ]
        );

        addedLeads.push(result[0].id);
      }
    }

    return NextResponse.json({
      success: true,
      added: addedLeads.length,
      message: `Added ${addedLeads.length} new leads`,
    });
  } catch (error) {
    console.error("Database error:", error);
    return NextResponse.json({ error: "Failed to add leads" }, { status: 500 });
  }
}
