"use client";

import { useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, Settings as SettingsIcon, Lock } from "lucide-react";
import { logActivity, LogAction } from "@/lib/logger";

interface SettingsState {
  // API Configuration
  apiKey: string;
  scanFrequency: string;
  apiRateLimit: string;
  apiTimeout: string;

  // Notifications
  notificationEmail: string;
  emailNotifications: boolean;
  slackWebhook: string;
  slackNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  notificationFrequency: string;

  // Data Sync
  autoSync: boolean;
  syncFrequency: string;
  backupEnabled: boolean;
  backupFrequency: string;
  dataRetention: string;

  // Search Preferences
  defaultRadius: string;
  maxResults: string;
  searchTimeout: string;
  cacheResults: boolean;

  // Display Preferences
  language: string;
  defaultView: string;
  theme: string;
  timezone: string;
  dateFormat: string;

  // Security
  twoFactorAuth: boolean;
  sessionTimeout: string;
  ipWhitelist: string;
  auditLogging: boolean;

  // Performance
  batchSize: string;
  concurrentRequests: string;
  cacheExpiry: string;

  // Integrations
  erpIntegration: boolean;
  erpEndpoint: string;
  webhookUrl: string;
  exportFormat: string;
}

type SettingsKey = keyof SettingsState;

export default function Settings() {
  const [settings, setSettings] = useState<SettingsState>({
    // API Configuration
    apiKey: "sk-1234567890abcdef...",
    scanFrequency: "daily",
    apiRateLimit: "1000",
    apiTimeout: "30",

    // Notifications
    notificationEmail: "<EMAIL>",
    emailNotifications: true,
    slackWebhook: "https://hooks.slack.com/services/...",
    slackNotifications: false,
    pushNotifications: true,
    smsNotifications: false,
    notificationFrequency: "immediate",

    // Data Sync
    autoSync: true,
    syncFrequency: "daily",
    backupEnabled: true,
    backupFrequency: "weekly",
    dataRetention: "365",

    // Search Preferences
    defaultRadius: "1000",
    maxResults: "20",
    searchTimeout: "30",
    cacheResults: true,

    // Display Preferences
    language: "en",
    defaultView: "list",
    theme: "light",
    timezone: "Europe/Zurich",
    dateFormat: "DD/MM/YYYY",

    // Security
    twoFactorAuth: false,
    sessionTimeout: "60",
    ipWhitelist: "***********/24",
    auditLogging: true,

    // Performance
    batchSize: "50",
    concurrentRequests: "5",
    cacheExpiry: "3600",

    // Integrations
    erpIntegration: false,
    erpEndpoint: "https://api.erp-system.com",
    webhookUrl: "https://webhook.site/...",
    exportFormat: "csv",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Here you would typically send this data to your backend
      // const response = await fetch("/api/settings", {
      //   method: "POST",
      //   headers: { "Content-Type": "application/json" },
      //   body: JSON.stringify(settings),
      // })
      console.log("Settings submitted:", settings);
      toast.success("Settings saved successfully");
    } catch (error) {
      console.error("Error saving settings:", error);
      toast.error("Failed to save settings");
    }
  };

  const handleSettingsUpdate = async (newSettings: SettingsState) => {
    try {
      await logActivity(
        LogAction.SETTINGS_UPDATE,
        "Settings updated successfully",
        {
          previous_settings: settings,
          new_settings: newSettings,
          changed_fields: (Object.keys(newSettings) as SettingsKey[]).filter(
            (key) => newSettings[key] !== settings[key]
          ),
        }
      );

      setSettings(newSettings);
    } catch (error) {
      console.error("Error updating settings:", error);

      await logActivity(
        LogAction.SETTINGS_UPDATE,
        "Failed to update settings",
        {
          attempted_settings: newSettings,
          error: error instanceof Error ? error.message : "Unknown error",
        }
      );
    }
  };

  return (
    <div className="max-w-6xl mx-auto py-6 space-y-8">
      {/* Not Implemented Banner */}
      <Card className="border-amber-200 bg-amber-50 dark:bg-amber-950/20">
        <CardContent className="pt-6">
          <div className="flex items-center gap-3 mb-4">
            <AlertTriangle className="h-6 w-6 text-amber-600" />
            <div>
              <h3 className="text-lg font-semibold text-amber-800 dark:text-amber-200">
                Settings - TBI (To Be Implemented)
              </h3>
              <p className="text-amber-700 dark:text-amber-300">
                This settings interface shows the planned configuration options
                for the application. All settings can be implemented in the next
                sprint and currently serve as a preview of future functionality.
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Badge
              variant="outline"
              className="border-amber-300 text-amber-700"
            >
              <Lock className="h-3 w-3 mr-1" />
              Preview Mode
            </Badge>
            <Badge
              variant="outline"
              className="border-amber-300 text-amber-700"
            >
              <SettingsIcon className="h-3 w-3 mr-1" />
              Future Feature
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Settings Form - Disabled State */}
      <div className="filter blur-[1px] grayscale opacity-60 pointer-events-none select-none">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* API Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>API Configuration</CardTitle>
              <CardDescription>
                Configure your Google Maps API settings and scanning preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="api-key">Google Maps API Key</Label>
                <Input
                  id="api-key"
                  value={settings.apiKey}
                  onChange={(e) =>
                    setSettings({ ...settings, apiKey: e.target.value })
                  }
                  placeholder="Enter your API key"
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="scan-frequency">Scan Frequency</Label>
                <Select
                  value={settings.scanFrequency}
                  onValueChange={(value) =>
                    setSettings({ ...settings, scanFrequency: value })
                  }
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hourly">Hourly</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="api-rate-limit">
                  API Rate Limit (requests/hour)
                </Label>
                <Input
                  id="api-rate-limit"
                  type="number"
                  value={settings.apiRateLimit}
                  onChange={(e) =>
                    setSettings({ ...settings, apiRateLimit: e.target.value })
                  }
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="api-timeout">API Timeout (seconds)</Label>
                <Input
                  id="api-timeout"
                  type="number"
                  value={settings.apiTimeout}
                  onChange={(e) =>
                    setSettings({ ...settings, apiTimeout: e.target.value })
                  }
                  disabled
                />
              </div>
            </CardContent>
          </Card>

          {/* Notification Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>
                Configure how you want to receive notifications about new leads
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications about new leads via email
                  </p>
                </div>
                <Switch
                  checked={settings.emailNotifications}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, emailNotifications: checked })
                  }
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="notification-email">Notification Email</Label>
                <Input
                  id="notification-email"
                  type="email"
                  value={settings.notificationEmail}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      notificationEmail: e.target.value,
                    })
                  }
                  placeholder="Enter email address"
                  disabled
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Slack Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications in your Slack channel
                  </p>
                </div>
                <Switch
                  checked={settings.slackNotifications}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, slackNotifications: checked })
                  }
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="slack-webhook">Slack Webhook URL</Label>
                <Input
                  id="slack-webhook"
                  value={settings.slackWebhook}
                  onChange={(e) =>
                    setSettings({ ...settings, slackWebhook: e.target.value })
                  }
                  placeholder="Enter Slack webhook URL"
                  disabled
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Push Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive browser push notifications
                  </p>
                </div>
                <Switch
                  checked={settings.pushNotifications}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, pushNotifications: checked })
                  }
                  disabled
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>SMS Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive SMS notifications for urgent alerts
                  </p>
                </div>
                <Switch
                  checked={settings.smsNotifications}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, smsNotifications: checked })
                  }
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="notification-frequency">
                  Notification Frequency
                </Label>
                <Select
                  value={settings.notificationFrequency}
                  onValueChange={(value) =>
                    setSettings({ ...settings, notificationFrequency: value })
                  }
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="immediate">Immediate</SelectItem>
                    <SelectItem value="hourly">Hourly Digest</SelectItem>
                    <SelectItem value="daily">Daily Digest</SelectItem>
                    <SelectItem value="weekly">Weekly Summary</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Data Synchronization */}
          <Card>
            <CardHeader>
              <CardTitle>Data Synchronization</CardTitle>
              <CardDescription>
                Configure how your lead data syncs with external systems
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Automatic Sync</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically sync lead data with ERP system
                  </p>
                </div>
                <Switch
                  checked={settings.autoSync}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, autoSync: checked })
                  }
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="sync-frequency">Sync Frequency</Label>
                <Select
                  value={settings.syncFrequency}
                  onValueChange={(value) =>
                    setSettings({ ...settings, syncFrequency: value })
                  }
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hourly">Hourly</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Backup Enabled</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically backup lead data
                  </p>
                </div>
                <Switch
                  checked={settings.backupEnabled}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, backupEnabled: checked })
                  }
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="backup-frequency">Backup Frequency</Label>
                <Select
                  value={settings.backupFrequency}
                  onValueChange={(value) =>
                    setSettings({ ...settings, backupFrequency: value })
                  }
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="data-retention">Data Retention (days)</Label>
                <Input
                  id="data-retention"
                  type="number"
                  value={settings.dataRetention}
                  onChange={(e) =>
                    setSettings({ ...settings, dataRetention: e.target.value })
                  }
                  disabled
                />
              </div>
            </CardContent>
          </Card>

          {/* Search Preferences */}
          <Card>
            <CardHeader>
              <CardTitle>Search Preferences</CardTitle>
              <CardDescription>
                Configure default settings for lead generation searches
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="default-radius">
                  Default Search Radius (meters)
                </Label>
                <Input
                  id="default-radius"
                  type="number"
                  value={settings.defaultRadius}
                  onChange={(e) =>
                    setSettings({ ...settings, defaultRadius: e.target.value })
                  }
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="max-results">Maximum Results per Search</Label>
                <Input
                  id="max-results"
                  type="number"
                  value={settings.maxResults}
                  onChange={(e) =>
                    setSettings({ ...settings, maxResults: e.target.value })
                  }
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="search-timeout">Search Timeout (seconds)</Label>
                <Input
                  id="search-timeout"
                  type="number"
                  value={settings.searchTimeout}
                  onChange={(e) =>
                    setSettings({ ...settings, searchTimeout: e.target.value })
                  }
                  disabled
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Cache Search Results</Label>
                  <p className="text-sm text-muted-foreground">
                    Cache search results to improve performance
                  </p>
                </div>
                <Switch
                  checked={settings.cacheResults}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, cacheResults: checked })
                  }
                  disabled
                />
              </div>
            </CardContent>
          </Card>

          {/* Display Preferences */}
          <Card>
            <CardHeader>
              <CardTitle>Display Preferences</CardTitle>
              <CardDescription>
                Configure how you want the application to look and behave
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="language">Language</Label>
                <Select
                  value={settings.language}
                  onValueChange={(value) =>
                    setSettings({ ...settings, language: value })
                  }
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="de">German</SelectItem>
                    <SelectItem value="fr">French</SelectItem>
                    <SelectItem value="it">Italian</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="default-view">Default View</Label>
                <Select
                  value={settings.defaultView}
                  onValueChange={(value) =>
                    setSettings({ ...settings, defaultView: value })
                  }
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select default view" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="list">List View</SelectItem>
                    <SelectItem value="grid">Grid View</SelectItem>
                    <SelectItem value="map">Map View</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="theme">Theme</Label>
                <Select
                  value={settings.theme}
                  onValueChange={(value) =>
                    setSettings({ ...settings, theme: value })
                  }
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select theme" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="timezone">Timezone</Label>
                <Select
                  value={settings.timezone}
                  onValueChange={(value) =>
                    setSettings({ ...settings, timezone: value })
                  }
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select timezone" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Europe/Zurich">Europe/Zurich</SelectItem>
                    <SelectItem value="Europe/Berlin">Europe/Berlin</SelectItem>
                    <SelectItem value="Europe/Paris">Europe/Paris</SelectItem>
                    <SelectItem value="UTC">UTC</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="date-format">Date Format</Label>
                <Select
                  value={settings.dateFormat}
                  onValueChange={(value) =>
                    setSettings({ ...settings, dateFormat: value })
                  }
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select date format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                    <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                    <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Security Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>
                Configure security and access control settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Two-Factor Authentication</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable 2FA for enhanced security
                  </p>
                </div>
                <Switch
                  checked={settings.twoFactorAuth}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, twoFactorAuth: checked })
                  }
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="session-timeout">
                  Session Timeout (minutes)
                </Label>
                <Input
                  id="session-timeout"
                  type="number"
                  value={settings.sessionTimeout}
                  onChange={(e) =>
                    setSettings({ ...settings, sessionTimeout: e.target.value })
                  }
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="ip-whitelist">IP Whitelist</Label>
                <Textarea
                  id="ip-whitelist"
                  value={settings.ipWhitelist}
                  onChange={(e) =>
                    setSettings({ ...settings, ipWhitelist: e.target.value })
                  }
                  placeholder="Enter IP addresses or ranges (one per line)"
                  disabled
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Audit Logging</Label>
                  <p className="text-sm text-muted-foreground">
                    Log all user actions for security auditing
                  </p>
                </div>
                <Switch
                  checked={settings.auditLogging}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, auditLogging: checked })
                  }
                  disabled
                />
              </div>
            </CardContent>
          </Card>

          {/* Performance Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Settings</CardTitle>
              <CardDescription>
                Configure performance and optimization settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="batch-size">Batch Size</Label>
                <Input
                  id="batch-size"
                  type="number"
                  value={settings.batchSize}
                  onChange={(e) =>
                    setSettings({ ...settings, batchSize: e.target.value })
                  }
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="concurrent-requests">Concurrent Requests</Label>
                <Input
                  id="concurrent-requests"
                  type="number"
                  value={settings.concurrentRequests}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      concurrentRequests: e.target.value,
                    })
                  }
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cache-expiry">Cache Expiry (seconds)</Label>
                <Input
                  id="cache-expiry"
                  type="number"
                  value={settings.cacheExpiry}
                  onChange={(e) =>
                    setSettings({ ...settings, cacheExpiry: e.target.value })
                  }
                  disabled
                />
              </div>
            </CardContent>
          </Card>

          {/* Integration Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Integration Settings</CardTitle>
              <CardDescription>
                Configure external system integrations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>ERP Integration</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable integration with ERP system
                  </p>
                </div>
                <Switch
                  checked={settings.erpIntegration}
                  onCheckedChange={(checked) =>
                    setSettings({ ...settings, erpIntegration: checked })
                  }
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="erp-endpoint">ERP Endpoint URL</Label>
                <Input
                  id="erp-endpoint"
                  value={settings.erpEndpoint}
                  onChange={(e) =>
                    setSettings({ ...settings, erpEndpoint: e.target.value })
                  }
                  placeholder="https://api.erp-system.com"
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="webhook-url">Webhook URL</Label>
                <Input
                  id="webhook-url"
                  value={settings.webhookUrl}
                  onChange={(e) =>
                    setSettings({ ...settings, webhookUrl: e.target.value })
                  }
                  placeholder="https://webhook.site/..."
                  disabled
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="export-format">Default Export Format</Label>
                <Select
                  value={settings.exportFormat}
                  onValueChange={(value) =>
                    setSettings({ ...settings, exportFormat: value })
                  }
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="csv">CSV</SelectItem>
                    <SelectItem value="xlsx">Excel (XLSX)</SelectItem>
                    <SelectItem value="json">JSON</SelectItem>
                    <SelectItem value="xml">XML</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button type="submit" size="lg" disabled>
              Save Settings
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
