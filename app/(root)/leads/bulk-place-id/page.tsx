"use client";

import React, { useState, useEffect } from "react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  AlertCircle,
  Loader2,
  MapPin,
  CheckCircle,
  XCircle,
  Search,
  Database,
  ArrowLeft,
  ExternalLink,
  X,
} from "lucide-react";
import Link from "next/link";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { logActivity, LogAction } from "@/lib/logger";

interface LeadWithoutPlaceId {
  id: number;
  name: string;
  formatted_address: string;
  street_name: string;
  street_number: string;
  city: string;
  postal_code: string;
  canton: string;
  country: string;
  international_phone: string | null;
  national_phone: string | null;
  location: {
    latitude: number;
    longitude: number;
  } | null;
}

interface PlaceIdSearchResult {
  leadId: number;
  leadName: string;
  placeId: string | null;
  googleName: string | null;
  confidence: "high" | "medium" | "failed";
  searchQuery: string;
  reason?: string;
}

interface SearchProgress {
  total: number;
  completed: number;
  successful: number;
  failed: number;
  inProgress: boolean;
}

interface UpdateProgress {
  total: number;
  completed: number;
  successful: number;
  failed: number;
  inProgress: boolean;
}

export default function BulkPlaceIdPage() {
  const [leadsWithoutPlaceId, setLeadsWithoutPlaceId] = useState<
    LeadWithoutPlaceId[]
  >([]);
  const [searchResults, setSearchResults] = useState<PlaceIdSearchResult[]>([]);
  const [loading, setLoading] = useState(true);
  const [searching, setSearching] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [progress, setProgress] = useState<SearchProgress>({
    total: 0,
    completed: 0,
    successful: 0,
    failed: 0,
    inProgress: false,
  });
  const [updateProgress, setUpdateProgress] = useState<UpdateProgress>({
    total: 0,
    completed: 0,
    successful: 0,
    failed: 0,
    inProgress: false,
  });
  const [selectedResults, setSelectedResults] = useState<Set<number>>(
    new Set()
  );
  const [error, setError] = useState<string | null>(null);
  const [abortController, setAbortController] =
    useState<AbortController | null>(null);

  // Load leads without Place IDs on component mount
  useEffect(() => {
    loadLeadsWithoutPlaceId();
  }, []);

  const loadLeadsWithoutPlaceId = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/leads/without-place-ids");
      if (!response.ok) {
        throw new Error("Failed to load leads without Place IDs");
      }

      const data = await response.json();
      setLeadsWithoutPlaceId(data.leads);

      await logActivity(
        LogAction.LEAD_GENERATION,
        `Loaded ${data.leads.length} leads without Google Place IDs`,
        { count: data.leads.length }
      );
    } catch (error) {
      console.error("Error loading leads:", error);
      setError(error instanceof Error ? error.message : "Unknown error");
      toast.error("Failed to load leads without Place IDs");
    } finally {
      setLoading(false);
    }
  };

  const stopBulkSearch = () => {
    if (abortController) {
      abortController.abort();
      setAbortController(null);
      setSearching(false);
      setProgress((prev) => ({ ...prev, inProgress: false }));
      toast.info("Search stopped by user");
    }
  };

  const startBulkSearch = async () => {
    if (leadsWithoutPlaceId.length === 0) {
      toast.error("No leads available for search");
      return;
    }

    try {
      setSearching(true);
      setSearchResults([]);
      setSelectedResults(new Set());

      // Reset both progress trackers and initialize search progress
      setProgress({
        total: leadsWithoutPlaceId.length,
        completed: 0,
        successful: 0,
        failed: 0,
        inProgress: true,
      });
      setUpdateProgress({
        total: 0,
        completed: 0,
        successful: 0,
        failed: 0,
        inProgress: false,
      });

      // Create abort controller for this search
      const controller = new AbortController();
      setAbortController(controller);

      // Split leads into batches to avoid payload size limits
      const BATCH_SIZE = 50; // Process 50 leads per batch to avoid 413 errors
      const batches = [];
      for (let i = 0; i < leadsWithoutPlaceId.length; i += BATCH_SIZE) {
        batches.push(leadsWithoutPlaceId.slice(i, i + BATCH_SIZE));
      }

      console.log(
        `Processing ${leadsWithoutPlaceId.length} leads in ${batches.length} batches of ${BATCH_SIZE}`
      );

      let totalCompleted = 0;
      let totalSuccessful = 0;
      let totalFailed = 0;

      // Process each batch sequentially
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];

        // Check if search was aborted
        if (controller.signal.aborted) {
          console.log("Search aborted during batch processing");
          break;
        }

        console.log(
          `Processing batch ${batchIndex + 1}/${batches.length} with ${
            batch.length
          } leads`
        );

        try {
          const response = await fetch("/api/leads/bulk-place-id-search", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              leads: batch,
              batchInfo: {
                batchIndex: batchIndex + 1,
                totalBatches: batches.length,
                batchSize: batch.length,
                totalLeads: leadsWithoutPlaceId.length,
              },
            }),
            signal: controller.signal,
          });

          if (!response.ok) {
            throw new Error(
              `Failed to process batch ${batchIndex + 1}: ${
                response.statusText
              }`
            );
          }

          // Process the streaming response for this batch
          const reader = response.body?.getReader();
          if (!reader) {
            throw new Error("No response body");
          }

          const decoder = new TextDecoder();
          let buffer = "";

          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split("\n");
            buffer = lines.pop() || "";

            for (const line of lines) {
              if (line.trim()) {
                try {
                  const data = JSON.parse(line);

                  if (data.type === "progress") {
                    // Update progress with cumulative totals across all batches
                    const batchCompleted = data.completed;
                    const batchSuccessful = data.successful;
                    const batchFailed = data.failed;

                    // Calculate cumulative progress
                    const cumulativeCompleted = totalCompleted + batchCompleted;
                    const cumulativeSuccessful =
                      totalSuccessful + batchSuccessful;
                    const cumulativeFailed = totalFailed + batchFailed;

                    setProgress((prev) => ({
                      ...prev,
                      completed: cumulativeCompleted,
                      successful: cumulativeSuccessful,
                      failed: cumulativeFailed,
                    }));
                  } else if (data.type === "result") {
                    setSearchResults((prev) => [...prev, data.result]);

                    // Auto-select all results with valid Place IDs (excluding "None")
                    if (data.result.placeId && data.result.placeId !== "None") {
                      setSelectedResults(
                        (prev) => new Set([...prev, data.result.leadId])
                      );
                    }
                  } else if (data.type === "complete") {
                    // Update totals for this batch
                    totalCompleted += data.totalProcessed || batch.length;
                    totalSuccessful += data.totalFound || 0;
                    totalFailed += data.totalFailed || 0;

                    console.log(
                      `Batch ${batchIndex + 1} completed: ${
                        data.totalProcessed
                      } processed, ${data.totalFound} found`
                    );
                  }
                } catch (e) {
                  console.error("Error parsing response line:", line, e);
                }
              }
            }
          }
        } catch (batchError) {
          console.error(
            `Error processing batch ${batchIndex + 1}:`,
            batchError
          );

          // Handle abort signal differently
          if (batchError instanceof Error && batchError.name === "AbortError") {
            console.log("Batch processing was aborted by user");
            break;
          } else {
            // For other errors, mark all leads in this batch as failed and continue
            totalCompleted += batch.length;
            totalFailed += batch.length;

            setProgress((prev) => ({
              ...prev,
              completed: totalCompleted,
              failed: totalFailed,
            }));

            toast.error(
              `Batch ${batchIndex + 1} failed: ${
                batchError instanceof Error
                  ? batchError.message
                  : "Unknown error"
              }`
            );

            // Add a small delay before retrying next batch
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
        }
      }

      // Final progress update
      setProgress((prev) => ({
        ...prev,
        inProgress: false,
        completed: totalCompleted,
        successful: totalSuccessful,
        failed: totalFailed,
      }));

      if (!controller.signal.aborted) {
        toast.success(
          `Search completed! Processed ${totalCompleted} leads, found ${totalSuccessful} Place IDs`
        );

        await logActivity(
          LogAction.LEAD_GENERATION,
          `Completed bulk Place ID search for ${leadsWithoutPlaceId.length} leads in ${batches.length} batches`,
          {
            totalLeads: leadsWithoutPlaceId.length,
            totalBatches: batches.length,
            batchSize: BATCH_SIZE,
            successful: totalSuccessful,
            failed: totalFailed,
          }
        );
      }
    } catch (error) {
      console.error("Error during bulk search:", error);

      // Handle abort signal differently
      if (error instanceof Error && error.name === "AbortError") {
        // Search was stopped by user, don't show error
        console.log("Search was aborted by user");
      } else {
        setError(error instanceof Error ? error.message : "Unknown error");
        toast.error("Failed to complete bulk search");
      }

      setProgress((prev) => ({ ...prev, inProgress: false }));
    } finally {
      setSearching(false);
      setAbortController(null);
    }
  };

  const updateSelectedPlaceIds = async () => {
    const selectedResultsData = searchResults.filter(
      (result) => selectedResults.has(result.leadId) && result.placeId
    );

    if (selectedResultsData.length === 0) {
      toast.error("No Place IDs selected for update");
      return;
    }

    // Reset update progress before starting
    setUpdateProgress({
      total: 0,
      completed: 0,
      successful: 0,
      failed: 0,
      inProgress: false,
    });

    try {
      setUpdating(true);

      const updates = selectedResultsData.map((result) => ({
        leadId: result.leadId,
        placeId: result.placeId,
        googleName: result.googleName,
      }));

      // Initialize update progress tracking
      setUpdateProgress({
        total: updates.length,
        completed: 0,
        successful: 0,
        failed: 0,
        inProgress: true,
      });

      // Split updates into batches to avoid payload size limits
      const UPDATE_BATCH_SIZE = 30; // Process 30 updates per batch
      const updateBatches = [];
      for (let i = 0; i < updates.length; i += UPDATE_BATCH_SIZE) {
        updateBatches.push(updates.slice(i, i + UPDATE_BATCH_SIZE));
      }

      console.log(
        `Updating ${updates.length} Place IDs in ${updateBatches.length} batches`
      );

      let totalUpdated = 0;
      let totalFailed = 0;

      // Process each batch sequentially
      for (
        let batchIndex = 0;
        batchIndex < updateBatches.length;
        batchIndex++
      ) {
        const batch = updateBatches[batchIndex];

        console.log(
          `Processing update batch ${batchIndex + 1}/${
            updateBatches.length
          } with ${batch.length} updates`
        );

        try {
          const response = await fetch(
            "/api/leads/bulk-update-confirmed-place-ids",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                updates: batch,
                batchInfo: {
                  batchIndex: batchIndex + 1,
                  totalBatches: updateBatches.length,
                  batchSize: batch.length,
                  totalUpdates: updates.length,
                },
              }),
            }
          );

          if (!response.ok) {
            throw new Error(
              `Failed to update batch ${batchIndex + 1}: ${response.statusText}`
            );
          }

          const data = await response.json();
          const batchUpdated = data.updated || 0;
          const batchFailed = batch.length - batchUpdated;

          totalUpdated += batchUpdated;
          totalFailed += batchFailed;

          // Update progress with cumulative totals
          setUpdateProgress((prev) => ({
            ...prev,
            completed: totalUpdated + totalFailed,
            successful: totalUpdated,
            failed: totalFailed,
          }));

          console.log(
            `Update batch ${
              batchIndex + 1
            } completed: ${batchUpdated} updated, ${batchFailed} failed`
          );
        } catch (batchError) {
          console.error(`Error updating batch ${batchIndex + 1}:`, batchError);
          totalFailed += batch.length;

          // Update progress with failed batch
          setUpdateProgress((prev) => ({
            ...prev,
            completed: totalUpdated + totalFailed,
            successful: totalUpdated,
            failed: totalFailed,
          }));

          toast.error(
            `Update batch ${batchIndex + 1} failed: ${
              batchError instanceof Error ? batchError.message : "Unknown error"
            }`
          );

          // Add a small delay before retrying next batch
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      // Final update progress
      setUpdateProgress((prev) => ({
        ...prev,
        inProgress: false,
        completed: totalUpdated + totalFailed,
        successful: totalUpdated,
        failed: totalFailed,
      }));

      if (totalUpdated > 0) {
        toast.success(
          `Successfully updated ${totalUpdated} leads with Google Place IDs${
            totalFailed > 0 ? ` (${totalFailed} failed)` : ""
          }`
        );

        await logActivity(
          LogAction.LEAD_GENERATION,
          `Updated ${totalUpdated} leads with confirmed Google Place IDs in ${updateBatches.length} batches`,
          {
            updatedCount: totalUpdated,
            failedCount: totalFailed,
            totalBatches: updateBatches.length,
            batchSize: UPDATE_BATCH_SIZE,
          }
        );

        // Refresh the leads list
        await loadLeadsWithoutPlaceId();

        // Clear search results and reset progress
        setSearchResults([]);
        setSelectedResults(new Set());
        setProgress({
          total: 0,
          completed: 0,
          successful: 0,
          failed: 0,
          inProgress: false,
        });
        setUpdateProgress({
          total: 0,
          completed: 0,
          successful: 0,
          failed: 0,
          inProgress: false,
        });
      } else {
        toast.error("No Place IDs were updated");
      }
    } catch (error) {
      console.error("Error updating Place IDs:", error);
      toast.error("Failed to update Place IDs");

      // Reset update progress on error
      setUpdateProgress((prev) => ({
        ...prev,
        inProgress: false,
      }));
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading leads without Place IDs...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex flex-col space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/leads">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Leads
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold">
                Bulk Google Places ID Assignment
              </h1>
              <p className="text-muted-foreground">
                Find and assign Google Place IDs to leads that currently lack
                them
              </p>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Statistics Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-5 w-5" />
              <span>Current Status</span>
            </CardTitle>
            <CardDescription>
              Overview of leads requiring Google Place ID assignment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {leadsWithoutPlaceId.length}
                </div>
                <div className="text-sm text-muted-foreground">
                  Leads without Place ID
                </div>
              </div>
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {
                    searchResults.filter(
                      (r) => r.placeId && r.placeId !== "None"
                    ).length
                  }
                </div>
                <div className="text-sm text-muted-foreground">
                  Place IDs found
                </div>
              </div>
              <div className="text-center p-4 bg-muted/30 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {selectedResults.size}
                </div>
                <div className="text-sm text-muted-foreground">
                  Selected for update
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search Action Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="h-5 w-5" />
              <span>Bulk Search</span>
            </CardTitle>
            <CardDescription>
              Search for Google Place IDs using business names and addresses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">
                    This will search for Google Place IDs for all{" "}
                    {leadsWithoutPlaceId.length} leads using the cost-optimized
                    Google Places Text Search API (ID Only SKU) with location
                    bias when available. Large datasets are processed in batches
                    of 50 leads to ensure reliability.
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Trusts Google&apos;s search algorithm - all found Place IDs
                    will be auto-selected for update. Batch processing prevents
                    timeout and payload size issues.
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={startBulkSearch}
                    disabled={searching || leadsWithoutPlaceId.length === 0}
                    size="lg"
                  >
                    {searching ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Searching...
                      </>
                    ) : (
                      <>
                        <Search className="h-4 w-4 mr-2" />
                        Start Search
                      </>
                    )}
                  </Button>

                  {searching && (
                    <Button
                      onClick={stopBulkSearch}
                      variant="outline"
                      size="lg"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Stop
                    </Button>
                  )}
                </div>
              </div>

              {/* Two-Phase Progress Display */}
              {(searching ||
                progress.inProgress ||
                updating ||
                updateProgress.inProgress) && (
                <div className="space-y-4 p-4 bg-muted/30 rounded-lg">
                  {/* Phase 1: Search Progress */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <Search className="h-4 w-4" />
                        <span>Phase 1: Fetching Place IDs from Google</span>
                      </div>
                      <span>
                        {progress.completed} of {progress.total}
                      </span>
                    </div>
                    <Progress
                      value={
                        progress.total > 0
                          ? (progress.completed / progress.total) * 100
                          : 0
                      }
                      className="h-2"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>
                        <span className="text-green-600">
                          {progress.successful} successful
                        </span>
                        {" • "}
                        <span className="text-red-600">
                          {progress.failed} failed
                        </span>
                      </span>
                      {progress.inProgress && (
                        <span className="flex items-center">
                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                          Searching...
                        </span>
                      )}
                      {!progress.inProgress && progress.total > 0 && (
                        <span className="text-green-600">✓ Complete</span>
                      )}
                    </div>
                  </div>

                  {/* Phase 2: Database Update Progress */}
                  {(updating ||
                    updateProgress.inProgress ||
                    updateProgress.total > 0) && (
                    <div className="space-y-3 border-t pt-3">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-2">
                          <Database className="h-4 w-4" />
                          <span>Phase 2: Updating Database</span>
                        </div>
                        <span>
                          {updateProgress.completed} of {updateProgress.total}
                        </span>
                      </div>
                      <Progress
                        value={
                          updateProgress.total > 0
                            ? (updateProgress.completed /
                                updateProgress.total) *
                              100
                            : 0
                        }
                        className="h-2"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>
                          <span className="text-green-600">
                            {updateProgress.successful} updated
                          </span>
                          {" • "}
                          <span className="text-red-600">
                            {updateProgress.failed} failed
                          </span>
                        </span>
                        {updateProgress.inProgress && (
                          <span className="flex items-center">
                            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                            Updating...
                          </span>
                        )}
                        {!updateProgress.inProgress &&
                          updateProgress.total > 0 && (
                            <span className="text-green-600">✓ Complete</span>
                          )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5" />
                  <span>Search Results</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const allFoundResults = searchResults
                        .filter((r) => r.placeId && r.placeId !== "None")
                        .map((r) => r.leadId);
                      setSelectedResults(new Set(allFoundResults));
                    }}
                  >
                    Select All Found
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const allResults = searchResults
                        .filter((r) => r.placeId)
                        .map((r) => r.leadId);
                      setSelectedResults(new Set(allResults));
                    }}
                  >
                    Select All (inc. None)
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedResults(new Set())}
                  >
                    Clear Selection
                  </Button>
                  <Button
                    onClick={updateSelectedPlaceIds}
                    disabled={updating || selectedResults.size === 0}
                  >
                    {updating ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Updating...
                      </>
                    ) : (
                      <>
                        <Database className="h-4 w-4 mr-2" />
                        Update {selectedResults.size} Place IDs
                      </>
                    )}
                  </Button>
                </div>
              </CardTitle>
              <CardDescription>
                Review and select Place IDs to update in the database. Leads
                with &quot;None&quot; will be marked as searched but no Place ID
                found.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {searchResults.map((result) => (
                  <div
                    key={result.leadId}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedResults.has(result.leadId)
                        ? "bg-blue-50 border-blue-200"
                        : "hover:bg-muted/30"
                    }`}
                    onClick={() => {
                      // Allow selection of any result (including "None" for marking as searched)
                      if (result.placeId) {
                        setSelectedResults((prev) => {
                          const newSet = new Set(prev);
                          if (newSet.has(result.leadId)) {
                            newSet.delete(result.leadId);
                          } else {
                            newSet.add(result.leadId);
                          }
                          return newSet;
                        });
                      }
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium">{result.leadName}</h4>
                          <Badge
                            variant={
                              result.confidence === "high"
                                ? "default"
                                : result.confidence === "medium"
                                ? "secondary"
                                : "destructive"
                            }
                          >
                            {result.confidence}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Query: {result.searchQuery}
                        </p>
                        {result.placeId && result.placeId !== "None" && (
                          <div className="space-y-1">
                            <p className="text-sm text-green-600">
                              Place ID: {result.placeId}
                            </p>
                            <a
                              href={`https://www.google.com/maps/search/?api=1&query=Google&query_place_id=${result.placeId}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 hover:underline"
                              onClick={(e) => e.stopPropagation()} // Prevent row selection when clicking link
                            >
                              <MapPin className="h-3 w-3 mr-1" />
                              View on Google Maps
                              <ExternalLink className="h-3 w-3 ml-1" />
                            </a>
                          </div>
                        )}
                        {result.placeId === "None" && (
                          <p className="text-sm text-orange-600">
                            No Place ID found - will be marked as searched
                          </p>
                        )}
                        {result.reason && (
                          <p className="text-sm text-red-600">
                            {result.reason}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        {result.placeId && result.placeId !== "None" ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : result.placeId === "None" ? (
                          <XCircle className="h-5 w-5 text-orange-600" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-600" />
                        )}
                        {result.placeId &&
                          selectedResults.has(result.leadId) && (
                            <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                              <CheckCircle className="h-3 w-3 text-white" />
                            </div>
                          )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {leadsWithoutPlaceId.length === 0 && !loading && (
          <Card>
            <CardContent className="text-center py-12">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                All leads have Place IDs!
              </h3>
              <p className="text-muted-foreground">
                All leads in your database already have Google Place IDs
                assigned.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
